using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using firstapp.Models;

namespace firstapp.Services
{
    public class ProductService
    {
        private readonly ObservableCollection<Product> _products = new();
        private readonly List<string> _categories = new();

        public ObservableCollection<Product> Products => _products;
        public IReadOnlyList<string> Categories => _categories.AsReadOnly();

        public ProductService()
        {
            LoadSampleData();
        }

        public IEnumerable<Product> GetProductsByCategory(string category)
        {
            if (string.IsNullOrEmpty(category) || category == "All")
                return _products;

            return _products.Where(p => p.Category.Equals(category, StringComparison.OrdinalIgnoreCase));
        }

        public IEnumerable<Product> SearchProducts(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return _products;

            searchTerm = searchTerm.ToLower();
            return _products.Where(p => 
                p.Name.ToLower().Contains(searchTerm) ||
                p.Description.ToLower().Contains(searchTerm) ||
                p.Category.ToLower().Contains(searchTerm));
        }

        public Product? GetProductById(int id)
        {
            return _products.FirstOrDefault(p => p.Id == id);
        }

        private void LoadSampleData()
        {
            var sampleProducts = new List<Product>
            {
                // Electronics
                new Product { Id = 1, Name = "Wireless Headphones", Price = 79.99m, Category = "Electronics", Description = "High-quality wireless headphones with noise cancellation", StockQuantity = 25 },
                new Product { Id = 2, Name = "Smartphone Case", Price = 24.99m, Category = "Electronics", Description = "Protective case for smartphones", StockQuantity = 50 },
                new Product { Id = 3, Name = "USB Cable", Price = 12.99m, Category = "Electronics", Description = "USB-C charging cable", StockQuantity = 100 },
                new Product { Id = 4, Name = "Bluetooth Speaker", Price = 45.99m, Category = "Electronics", Description = "Portable Bluetooth speaker", StockQuantity = 30 },
                new Product { Id = 5, Name = "Power Bank", Price = 29.99m, Category = "Electronics", Description = "10000mAh portable power bank", StockQuantity = 40 },

                // Clothing
                new Product { Id = 6, Name = "Cotton T-Shirt", Price = 19.99m, Category = "Clothing", Description = "100% cotton comfortable t-shirt", StockQuantity = 75 },
                new Product { Id = 7, Name = "Denim Jeans", Price = 59.99m, Category = "Clothing", Description = "Classic blue denim jeans", StockQuantity = 35 },
                new Product { Id = 8, Name = "Running Shoes", Price = 89.99m, Category = "Clothing", Description = "Comfortable running shoes", StockQuantity = 20 },
                new Product { Id = 9, Name = "Winter Jacket", Price = 129.99m, Category = "Clothing", Description = "Warm winter jacket", StockQuantity = 15 },
                new Product { Id = 10, Name = "Baseball Cap", Price = 24.99m, Category = "Clothing", Description = "Adjustable baseball cap", StockQuantity = 60 },

                // Books
                new Product { Id = 11, Name = "Programming Guide", Price = 39.99m, Category = "Books", Description = "Complete programming guide for beginners", StockQuantity = 25 },
                new Product { Id = 12, Name = "Mystery Novel", Price = 14.99m, Category = "Books", Description = "Bestselling mystery novel", StockQuantity = 40 },
                new Product { Id = 13, Name = "Cookbook", Price = 29.99m, Category = "Books", Description = "Healthy recipes cookbook", StockQuantity = 30 },
                new Product { Id = 14, Name = "Travel Guide", Price = 22.99m, Category = "Books", Description = "Complete travel guide to Europe", StockQuantity = 20 },
                new Product { Id = 15, Name = "Art Book", Price = 49.99m, Category = "Books", Description = "Beautiful art photography book", StockQuantity = 15 },

                // Home & Garden
                new Product { Id = 16, Name = "Coffee Mug", Price = 12.99m, Category = "Home & Garden", Description = "Ceramic coffee mug", StockQuantity = 80 },
                new Product { Id = 17, Name = "Plant Pot", Price = 18.99m, Category = "Home & Garden", Description = "Decorative ceramic plant pot", StockQuantity = 45 },
                new Product { Id = 18, Name = "Candle Set", Price = 34.99m, Category = "Home & Garden", Description = "Set of 3 scented candles", StockQuantity = 35 },
                new Product { Id = 19, Name = "Kitchen Knife", Price = 42.99m, Category = "Home & Garden", Description = "Professional chef's knife", StockQuantity = 25 },
                new Product { Id = 20, Name = "Throw Pillow", Price = 26.99m, Category = "Home & Garden", Description = "Decorative throw pillow", StockQuantity = 50 }
            };

            foreach (var product in sampleProducts)
            {
                _products.Add(product);
            }

            // Extract unique categories
            _categories.Add("All");
            _categories.AddRange(_products.Select(p => p.Category).Distinct().OrderBy(c => c));
        }
    }
}
