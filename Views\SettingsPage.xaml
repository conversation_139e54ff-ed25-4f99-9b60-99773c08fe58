<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="firstapp.Views.SettingsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:firstapp.Views"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <ScrollViewer>
        <Grid Padding="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <TextBlock Grid.Row="0" 
                       Text="Settings" 
                       Style="{StaticResource TitleTextBlockStyle}" 
                       Margin="0,0,0,20"/>

            <!-- Settings Content -->
            <StackPanel Grid.Row="1" MaxWidth="600" HorizontalAlignment="Left">
                
                <!-- Store Information -->
                <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                        CornerRadius="8"
                        Padding="20"
                        Margin="0,0,0,20">
                    <Border.Shadow>
                        <ThemeShadow />
                    </Border.Shadow>

                    <StackPanel>
                        <TextBlock Text="Store Information" 
                                   Style="{StaticResource SubtitleTextBlockStyle}"
                                   Margin="0,0,0,16"/>

                        <TextBox x:Name="StoreNameTextBox"
                                 Header="Store Name"
                                 Text="Retail POS System"
                                 Margin="0,0,0,12"/>

                        <TextBox x:Name="StoreAddressTextBox"
                                 Header="Address"
                                 Text="123 Main Street"
                                 Margin="0,0,0,12"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="StoreCityTextBox"
                                     Grid.Column="0"
                                     Header="City"
                                     Text="City"/>

                            <TextBox x:Name="StoreStateTextBox"
                                     Grid.Column="2"
                                     Header="State"
                                     Text="State"/>
                        </Grid>

                        <Grid Margin="0,12,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="10"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <TextBox x:Name="StoreZipTextBox"
                                     Grid.Column="0"
                                     Header="ZIP Code"
                                     Text="12345"/>

                            <TextBox x:Name="StorePhoneTextBox"
                                     Grid.Column="2"
                                     Header="Phone"
                                     Text="(*************"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Tax Settings -->
                <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                        CornerRadius="8"
                        Padding="20"
                        Margin="0,0,0,20">
                    <Border.Shadow>
                        <ThemeShadow />
                    </Border.Shadow>

                    <StackPanel>
                        <TextBlock Text="Tax Settings" 
                                   Style="{StaticResource SubtitleTextBlockStyle}"
                                   Margin="0,0,0,16"/>

                        <NumberBox x:Name="TaxRateNumberBox"
                                   Header="Tax Rate (%)"
                                   Value="8.0"
                                   Minimum="0"
                                   Maximum="100"
                                   SpinButtonPlacementMode="Inline"
                                   ValueChanged="TaxRateNumberBox_ValueChanged"/>

                        <TextBlock Text="Current tax rate will be applied to all new transactions"
                                   FontSize="12"
                                   Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"
                                   Margin="0,8,0,0"/>
                    </StackPanel>
                </Border>

                <!-- Receipt Settings -->
                <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                        CornerRadius="8"
                        Padding="20"
                        Margin="0,0,0,20">
                    <Border.Shadow>
                        <ThemeShadow />
                    </Border.Shadow>

                    <StackPanel>
                        <TextBlock Text="Receipt Settings" 
                                   Style="{StaticResource SubtitleTextBlockStyle}"
                                   Margin="0,0,0,16"/>

                        <CheckBox x:Name="AutoPrintReceiptCheckBox"
                                  Content="Automatically print receipt after payment"
                                  Margin="0,0,0,8"/>

                        <CheckBox x:Name="ShowReceiptPreviewCheckBox"
                                  Content="Show receipt preview before printing"
                                  IsChecked="True"
                                  Margin="0,0,0,8"/>

                        <CheckBox x:Name="EmailReceiptOptionCheckBox"
                                  Content="Always offer email receipt option"
                                  IsChecked="True"/>
                    </StackPanel>
                </Border>

                <!-- Display Settings -->
                <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                        CornerRadius="8"
                        Padding="20"
                        Margin="0,0,0,20">
                    <Border.Shadow>
                        <ThemeShadow />
                    </Border.Shadow>

                    <StackPanel>
                        <TextBlock Text="Display Settings" 
                                   Style="{StaticResource SubtitleTextBlockStyle}"
                                   Margin="0,0,0,16"/>

                        <ComboBox x:Name="ThemeComboBox"
                                  Header="Theme"
                                  SelectedIndex="0"
                                  Margin="0,0,0,12">
                            <ComboBoxItem Content="System Default"/>
                            <ComboBoxItem Content="Light"/>
                            <ComboBoxItem Content="Dark"/>
                        </ComboBox>

                        <Slider x:Name="FontSizeSlider"
                                Header="Font Size"
                                Minimum="12"
                                Maximum="20"
                                Value="14"
                                StepFrequency="1"
                                TickFrequency="2"
                                TickPlacement="BottomRight"/>
                    </StackPanel>
                </Border>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="Reset to Defaults"
                            Click="ResetToDefaultsButton_Click"
                            Margin="0,0,10,0"/>
                    <Button Content="Save Settings"
                            Style="{StaticResource AccentButtonStyle}"
                            Click="SaveSettingsButton_Click"/>
                </StackPanel>
            </StackPanel>
        </Grid>
    </ScrollViewer>
</Page>
