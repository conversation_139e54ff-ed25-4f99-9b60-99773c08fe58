using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;

namespace firstapp.Models
{
    public enum PaymentMethod
    {
        Cash,
        CreditCard,
        DebitCard,
        DigitalWallet,
        GiftCard
    }

    public enum TransactionStatus
    {
        Pending,
        Completed,
        Cancelled,
        Refunded
    }

    public class Transaction : INotifyPropertyChanged
    {
        private List<CartItem> _items = new();
        private Customer? _customer;
        private PaymentMethod _paymentMethod;
        private TransactionStatus _status = TransactionStatus.Pending;
        private decimal _taxRate = 0.08m; // 8% default tax rate
        private decimal _discountAmount = 0;
        private string _notes = string.Empty;

        public int Id { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
        public string TransactionNumber { get; set; } = Guid.NewGuid().ToString("N")[..8].ToUpper();

        public List<CartItem> Items
        {
            get => _items;
            set
            {
                _items = value ?? new List<CartItem>();
                OnPropertyChanged(nameof(Items));
                UpdateTotals();
            }
        }

        public Customer? Customer
        {
            get => _customer;
            set
            {
                _customer = value;
                OnPropertyChanged(nameof(Customer));
            }
        }

        public PaymentMethod PaymentMethod
        {
            get => _paymentMethod;
            set
            {
                _paymentMethod = value;
                OnPropertyChanged(nameof(PaymentMethod));
            }
        }

        public TransactionStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }

        public decimal TaxRate
        {
            get => _taxRate;
            set
            {
                _taxRate = value;
                OnPropertyChanged(nameof(TaxRate));
                UpdateTotals();
            }
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set
            {
                _discountAmount = Math.Max(0, value);
                OnPropertyChanged(nameof(DiscountAmount));
                UpdateTotals();
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                _notes = value;
                OnPropertyChanged(nameof(Notes));
            }
        }

        // Computed properties
        public decimal Subtotal => Items.Sum(item => item.Subtotal);
        public decimal TaxAmount => (Subtotal - DiscountAmount) * TaxRate;
        public decimal Total => Subtotal - DiscountAmount + TaxAmount;
        public int ItemCount => Items.Sum(item => item.Quantity);

        public string FormattedSubtotal => $"${Subtotal:F2}";
        public string FormattedTaxAmount => $"${TaxAmount:F2}";
        public string FormattedDiscountAmount => $"${DiscountAmount:F2}";
        public string FormattedTotal => $"${Total:F2}";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        private void UpdateTotals()
        {
            OnPropertyChanged(nameof(Subtotal));
            OnPropertyChanged(nameof(TaxAmount));
            OnPropertyChanged(nameof(Total));
            OnPropertyChanged(nameof(ItemCount));
            OnPropertyChanged(nameof(FormattedSubtotal));
            OnPropertyChanged(nameof(FormattedTaxAmount));
            OnPropertyChanged(nameof(FormattedTotal));
        }
    }
}
