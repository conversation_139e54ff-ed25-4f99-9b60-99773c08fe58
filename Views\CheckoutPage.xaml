<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="firstapp.Views.CheckoutPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:firstapp.Views"
    xmlns:models="using:firstapp.Models"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <ScrollViewer>
        <Grid Padding="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- Header -->
            <TextBlock Grid.Row="0" 
                       Text="Checkout" 
                       Style="{StaticResource TitleTextBlockStyle}" 
                       Margin="0,0,0,20"/>

            <!-- Main Content -->
            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*"/>
                    <ColumnDefinition Width="20"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- Left Column - Customer Information -->
                <StackPanel Grid.Column="0">
                    <!-- Customer Information Section -->
                    <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                            CornerRadius="8"
                            Padding="20"
                            Margin="0,0,0,20">
                        <Border.Shadow>
                            <ThemeShadow />
                        </Border.Shadow>

                        <StackPanel>
                            <TextBlock Text="Customer Information" 
                                       Style="{StaticResource SubtitleTextBlockStyle}"
                                       Margin="0,0,0,16"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="10"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="10"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="10"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="10"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>

                                <TextBox x:Name="FirstNameTextBox"
                                         Grid.Row="0" Grid.Column="0"
                                         PlaceholderText="First Name"
                                         Header="First Name"/>

                                <TextBox x:Name="LastNameTextBox"
                                         Grid.Row="0" Grid.Column="2"
                                         PlaceholderText="Last Name"
                                         Header="Last Name"/>

                                <TextBox x:Name="EmailTextBox"
                                         Grid.Row="2" Grid.Column="0"
                                         PlaceholderText="Email Address"
                                         Header="Email"
                                         InputScope="EmailNameOrAddress"/>

                                <TextBox x:Name="PhoneTextBox"
                                         Grid.Row="2" Grid.Column="2"
                                         PlaceholderText="Phone Number"
                                         Header="Phone"
                                         InputScope="TelephoneNumber"/>

                                <TextBox x:Name="AddressTextBox"
                                         Grid.Row="4" Grid.ColumnSpan="3"
                                         PlaceholderText="Address (Optional)"
                                         Header="Address"/>

                                <CheckBox x:Name="SaveCustomerCheckBox"
                                          Grid.Row="6" Grid.ColumnSpan="3"
                                          Content="Save customer information for future orders"
                                          Margin="0,10,0,0"/>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- Payment Method Selection -->
                    <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                            CornerRadius="8"
                            Padding="20">
                        <Border.Shadow>
                            <ThemeShadow />
                        </Border.Shadow>

                        <StackPanel>
                            <TextBlock Text="Payment Method" 
                                       Style="{StaticResource SubtitleTextBlockStyle}"
                                       Margin="0,0,0,16"/>

                            <StackPanel x:Name="PaymentMethodsPanel">
                                <RadioButton x:Name="CashRadioButton" 
                                             Content="Cash" 
                                             GroupName="PaymentMethod"
                                             IsChecked="True"
                                             Margin="0,0,0,8"/>
                                <RadioButton x:Name="CreditCardRadioButton" 
                                             Content="Credit Card" 
                                             GroupName="PaymentMethod"
                                             Margin="0,0,0,8"/>
                                <RadioButton x:Name="DebitCardRadioButton" 
                                             Content="Debit Card" 
                                             GroupName="PaymentMethod"
                                             Margin="0,0,0,8"/>
                                <RadioButton x:Name="DigitalWalletRadioButton" 
                                             Content="Digital Wallet" 
                                             GroupName="PaymentMethod"
                                             Margin="0,0,0,8"/>
                                <RadioButton x:Name="GiftCardRadioButton" 
                                             Content="Gift Card" 
                                             GroupName="PaymentMethod"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- Right Column - Order Summary -->
                <StackPanel Grid.Column="2">
                    <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                            CornerRadius="8"
                            Padding="20">
                        <Border.Shadow>
                            <ThemeShadow />
                        </Border.Shadow>

                        <StackPanel>
                            <TextBlock Text="Order Summary" 
                                       Style="{StaticResource SubtitleTextBlockStyle}"
                                       Margin="0,0,0,16"/>

                            <!-- Order Items -->
                            <ListView x:Name="OrderItemsListView"
                                      ItemsSource="{x:Bind CartService.Items, Mode=OneWay}"
                                      SelectionMode="None"
                                      MaxHeight="300"
                                      Margin="0,0,0,16">
                                
                                <ListView.ItemTemplate>
                                    <DataTemplate x:DataType="models:CartItem">
                                        <Grid Margin="0,4">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" 
                                                       Text="{x:Bind Product.Name}" 
                                                       TextWrapping="Wrap"
                                                       FontSize="14"/>
                                            <TextBlock Grid.Column="1" 
                                                       Text="{x:Bind Quantity}" 
                                                       FontSize="14"
                                                       Margin="8,0"/>
                                            <TextBlock Grid.Column="2" 
                                                       Text="{x:Bind FormattedSubtotal}" 
                                                       FontSize="14"
                                                       FontWeight="SemiBold"/>
                                        </Grid>
                                    </DataTemplate>
                                </ListView.ItemTemplate>
                            </ListView>

                            <!-- Discount Section -->
                            <StackPanel Margin="0,0,0,16">
                                <TextBlock Text="Discount" FontSize="14" FontWeight="SemiBold" Margin="0,0,0,8"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBox x:Name="DiscountTextBox"
                                             Grid.Column="0"
                                             PlaceholderText="0.00"
                                             InputScope="Number"
                                             TextChanged="DiscountTextBox_TextChanged"
                                             Margin="0,0,8,0"/>
                                    <Button Grid.Column="1"
                                            Content="Apply"
                                            Click="ApplyDiscountButton_Click"/>
                                </Grid>
                            </StackPanel>

                            <!-- Totals -->
                            <Border Height="1" Background="{ThemeResource SystemControlForegroundBaseLowBrush}" Margin="0,0,0,12"/>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Subtotal:" FontSize="14"/>
                                <TextBlock Grid.Column="1" Text="{x:Bind CartService.FormattedSubtotal, Mode=OneWay}" FontSize="14"/>
                            </Grid>

                            <Grid Margin="0,0,0,8" x:Name="DiscountGrid" Visibility="Collapsed">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Discount:" FontSize="14"/>
                                <TextBlock Grid.Column="1" Text="{x:Bind CartService.FormattedDiscountAmount, Mode=OneWay}" FontSize="14"/>
                            </Grid>

                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Tax:" FontSize="14"/>
                                <TextBlock Grid.Column="1" Text="{x:Bind CartService.FormattedTaxAmount, Mode=OneWay}" FontSize="14"/>
                            </Grid>

                            <Border Height="1" Background="{ThemeResource SystemControlForegroundBaseLowBrush}" Margin="0,8"/>

                            <Grid Margin="0,8,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Column="0" Text="Total:" FontSize="18" FontWeight="Bold"/>
                                <TextBlock Grid.Column="1" Text="{x:Bind CartService.FormattedTotal, Mode=OneWay}" FontSize="18" FontWeight="Bold"/>
                            </Grid>

                            <!-- Proceed to Payment Button -->
                            <Button Content="Proceed to Payment"
                                    HorizontalAlignment="Stretch"
                                    Style="{StaticResource AccentButtonStyle}"
                                    Click="ProceedToPaymentButton_Click"
                                    Height="48"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Grid>
        </Grid>
    </ScrollViewer>
</Page>
