using System;
using System.Collections.ObjectModel;
using System.Linq;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Navigation;
using firstapp.Models;
using firstapp.Services;

namespace firstapp.Views
{
    public sealed partial class ProductsPage : Page
    {
        private ProductService? _productService;
        private CartService? _cartService;
        private ObservableCollection<Product> _filteredProducts = new();
        private string _currentSearchTerm = string.Empty;
        private string _currentCategory = "All";

        public ObservableCollection<Product> FilteredProducts => _filteredProducts;

        public ProductsPage()
        {
            InitializeComponent();
        }

        protected override void OnNavigatedTo(NavigationEventArgs e)
        {
            base.OnNavigatedTo(e);

            if (e.Parameter is object param)
            {
                var properties = param.GetType().GetProperties();
                var productServiceProp = properties.FirstOrDefault(p => p.PropertyType == typeof(ProductService));
                var cartServiceProp = properties.FirstOrDefault(p => p.PropertyType == typeof(CartService));

                if (productServiceProp != null)
                    _productService = (ProductService?)productServiceProp.GetValue(param);
                
                if (cartServiceProp != null)
                    _cartService = (CartService?)cartServiceProp.GetValue(param);
            }

            if (_productService != null)
            {
                LoadCategories();
                LoadProducts();
            }
        }

        private void LoadCategories()
        {
            if (_productService == null) return;

            CategoryComboBox.Items.Clear();
            foreach (var category in _productService.Categories)
            {
                CategoryComboBox.Items.Add(category);
            }
            CategoryComboBox.SelectedIndex = 0; // Select "All"
        }

        private void LoadProducts()
        {
            if (_productService == null) return;

            var products = _productService.GetProductsByCategory(_currentCategory);
            
            if (!string.IsNullOrWhiteSpace(_currentSearchTerm))
            {
                products = _productService.SearchProducts(_currentSearchTerm)
                    .Where(p => _currentCategory == "All" || p.Category == _currentCategory);
            }

            _filteredProducts.Clear();
            foreach (var product in products)
            {
                _filteredProducts.Add(product);
            }
        }

        private void SearchBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            _currentSearchTerm = SearchBox.Text;
            LoadProducts();
        }

        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            LoadProducts();
        }

        private void CategoryComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CategoryComboBox.SelectedItem is string selectedCategory)
            {
                _currentCategory = selectedCategory;
                LoadProducts();
            }
        }

        private void ProductsGridView_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (e.ClickedItem is Product product && _cartService != null)
            {
                _cartService.AddItem(product);
                ShowAddedToCartNotification(product);
            }
        }

        private void AddToCartButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is Product product && _cartService != null)
            {
                _cartService.AddItem(product);
                ShowAddedToCartNotification(product);
            }
        }

        private void ShowAddedToCartNotification(Product product)
        {
            // Create a simple notification
            var infoBar = new InfoBar
            {
                Title = "Added to Cart",
                Message = $"{product.Name} has been added to your cart.",
                Severity = InfoBarSeverity.Success,
                IsOpen = true
            };

            // Add to the page temporarily
            if (Content is Grid mainGrid)
            {
                mainGrid.Children.Add(infoBar);
                Grid.SetRow(infoBar, 0);
                Grid.SetColumnSpan(infoBar, 3);

                // Auto-hide after 3 seconds
                var timer = new DispatcherTimer { Interval = TimeSpan.FromSeconds(3) };
                timer.Tick += (s, e) =>
                {
                    timer.Stop();
                    mainGrid.Children.Remove(infoBar);
                };
                timer.Start();
            }
        }
    }
}
