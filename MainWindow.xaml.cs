using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.WindowsRuntime;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Controls.Primitives;
using Microsoft.UI.Xaml.Data;
using Microsoft.UI.Xaml.Input;
using Microsoft.UI.Xaml.Media;
using Microsoft.UI.Xaml.Navigation;
using Windows.Foundation;
using Windows.Foundation.Collections;
// using firstapp.Views;
using firstapp.Services;

// To learn more about WinUI, the WinUI project structure,
// and more about our project templates, see: http://aka.ms/winui-project-info.

namespace firstapp
{
    /// <summary>
    /// Main window for the POS system with navigation between different screens.
    /// </summary>
    public sealed partial class MainWindow : Window
    {
        public ProductService ProductService { get; }
        public CartService CartService { get; }

        public MainWindow()
        {
            InitializeComponent();

            // Initialize services
            ProductService = new ProductService();
            CartService = new CartService();

            // Set default page
            MainNavigationView.SelectedItem = MainNavigationView.MenuItems[0];
            // ContentFrame.Navigate(typeof(ProductsPage), new { ProductService, CartService });
        }

        private void NavigationView_SelectionChanged(NavigationView sender, NavigationViewSelectionChangedEventArgs args)
        {
            if (args.SelectedItem is NavigationViewItem item)
            {
                var tag = item.Tag?.ToString();
                var pageType = tag switch
                {
                    "Products" => typeof(ProductsPage),
                    "Cart" => typeof(CartPage),
                    "Checkout" => typeof(CheckoutPage),
                    "Payment" => typeof(PaymentPage),
                    "Receipt" => typeof(ReceiptPage),
                    "Settings" => typeof(SettingsPage),
                    _ => null
                };

                if (pageType != null)
                {
                    // ContentFrame.Navigate(pageType, new { ProductService, CartService });
                }
            }
        }
    }
}
