<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="firstapp.Views.PaymentPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:firstapp.Views"
    xmlns:models="using:firstapp.Models"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid Padding="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                   Text="Payment Processing" 
                   Style="{StaticResource TitleTextBlockStyle}" 
                   Margin="0,0,0,20"/>

        <!-- Main Content -->
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="2*"/>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Column - Payment Interface -->
            <StackPanel Grid.Column="0">
                <!-- Payment Method Display -->
                <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                        CornerRadius="8"
                        Padding="20"
                        Margin="0,0,0,20">
                    <Border.Shadow>
                        <ThemeShadow />
                    </Border.Shadow>

                    <StackPanel>
                        <TextBlock Text="Payment Method" 
                                   Style="{StaticResource SubtitleTextBlockStyle}"
                                   Margin="0,0,0,16"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <FontIcon Grid.Column="0" 
                                      x:Name="PaymentMethodIcon"
                                      Glyph="&#xE8C7;" 
                                      FontSize="24"
                                      Margin="0,0,12,0"/>

                            <TextBlock Grid.Column="1" 
                                       x:Name="PaymentMethodText"
                                       Text="Cash Payment"
                                       FontSize="18"
                                       FontWeight="SemiBold"
                                       VerticalAlignment="Center"/>

                            <Button Grid.Column="2"
                                    Content="Change"
                                    Click="ChangePaymentMethodButton_Click"/>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Payment Details -->
                <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                        CornerRadius="8"
                        Padding="20"
                        Margin="0,0,0,20">
                    <Border.Shadow>
                        <ThemeShadow />
                    </Border.Shadow>

                    <StackPanel x:Name="PaymentDetailsPanel">
                        <TextBlock Text="Payment Details" 
                                   Style="{StaticResource SubtitleTextBlockStyle}"
                                   Margin="0,0,0,16"/>

                        <!-- Cash Payment Panel -->
                        <StackPanel x:Name="CashPaymentPanel" Visibility="Visible">
                            <Grid Margin="0,0,0,16">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="Amount Due" FontSize="14" Margin="0,0,0,4"/>
                                    <TextBlock x:Name="AmountDueText" 
                                               Text="{x:Bind CartService.FormattedTotal, Mode=OneWay}"
                                               FontSize="24"
                                               FontWeight="Bold"
                                               Foreground="{ThemeResource SystemAccentColor}"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="Amount Received" FontSize="14" Margin="0,0,0,4"/>
                                    <TextBox x:Name="CashReceivedTextBox"
                                             PlaceholderText="0.00"
                                             InputScope="Number"
                                             TextChanged="CashReceivedTextBox_TextChanged"
                                             FontSize="18"/>
                                </StackPanel>
                            </Grid>

                            <Grid x:Name="ChangePanel" Visibility="Collapsed" Margin="0,16,0,0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="Change Due" FontSize="14" Margin="0,0,0,4"/>
                                    <TextBlock x:Name="ChangeDueText" 
                                               FontSize="20"
                                               FontWeight="Bold"
                                               Foreground="Green"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>

                        <!-- Card Payment Panel -->
                        <StackPanel x:Name="CardPaymentPanel" Visibility="Collapsed">
                            <TextBlock Text="Please insert or swipe the card" 
                                       FontSize="16"
                                       HorizontalAlignment="Center"
                                       Margin="0,20"/>

                            <Border Background="{ThemeResource SystemControlBackgroundBaseLowBrush}"
                                    CornerRadius="8"
                                    Height="120"
                                    Margin="0,20">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <FontIcon Glyph="&#xE8C7;" FontSize="48" Margin="0,0,0,8"/>
                                    <TextBlock Text="Card Reader" HorizontalAlignment="Center"/>
                                    <ProgressRing x:Name="CardProcessingRing" 
                                                  IsActive="False" 
                                                  Width="30" 
                                                  Height="30"
                                                  Margin="0,8,0,0"/>
                                </StackPanel>
                            </Border>

                            <Button x:Name="SimulateCardButton"
                                    Content="Simulate Card Payment"
                                    HorizontalAlignment="Center"
                                    Click="SimulateCardButton_Click"
                                    Margin="0,16"/>
                        </StackPanel>

                        <!-- Digital Wallet Panel -->
                        <StackPanel x:Name="DigitalWalletPanel" Visibility="Collapsed">
                            <TextBlock Text="Please present device for contactless payment" 
                                       FontSize="16"
                                       HorizontalAlignment="Center"
                                       Margin="0,20"/>

                            <Border Background="{ThemeResource SystemControlBackgroundBaseLowBrush}"
                                    CornerRadius="8"
                                    Height="120"
                                    Margin="0,20">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <FontIcon Glyph="&#xE704;" FontSize="48" Margin="0,0,0,8"/>
                                    <TextBlock Text="NFC Reader" HorizontalAlignment="Center"/>
                                    <ProgressRing x:Name="WalletProcessingRing" 
                                                  IsActive="False" 
                                                  Width="30" 
                                                  Height="30"
                                                  Margin="0,8,0,0"/>
                                </StackPanel>
                            </Border>

                            <Button x:Name="SimulateWalletButton"
                                    Content="Simulate Digital Wallet Payment"
                                    HorizontalAlignment="Center"
                                    Click="SimulateWalletButton_Click"
                                    Margin="0,16"/>
                        </StackPanel>
                    </StackPanel>
                </Border>

                <!-- Action Buttons -->
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <Button Content="Cancel Transaction"
                            Click="CancelTransactionButton_Click"
                            Margin="0,0,10,0"/>
                    <Button x:Name="ProcessPaymentButton"
                            Content="Process Payment"
                            Style="{StaticResource AccentButtonStyle}"
                            Click="ProcessPaymentButton_Click"
                            IsEnabled="False"/>
                </StackPanel>
            </StackPanel>

            <!-- Right Column - Transaction Summary -->
            <Border Grid.Column="2" 
                    Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                    CornerRadius="8"
                    Padding="20">
                <Border.Shadow>
                    <ThemeShadow />
                </Border.Shadow>

                <StackPanel>
                    <TextBlock Text="Transaction Summary" 
                               Style="{StaticResource SubtitleTextBlockStyle}"
                               Margin="0,0,0,16"/>

                    <!-- Transaction Details -->
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Items:" FontSize="14"/>
                        <TextBlock Grid.Column="1" Text="{x:Bind CartService.ItemCount, Mode=OneWay}" FontSize="14"/>
                    </Grid>

                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Subtotal:" FontSize="14"/>
                        <TextBlock Grid.Column="1" Text="{x:Bind CartService.FormattedSubtotal, Mode=OneWay}" FontSize="14"/>
                    </Grid>

                    <Grid Margin="0,0,0,8" x:Name="TransactionDiscountGrid" Visibility="Collapsed">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Discount:" FontSize="14"/>
                        <TextBlock Grid.Column="1" Text="{x:Bind CartService.FormattedDiscountAmount, Mode=OneWay}" FontSize="14"/>
                    </Grid>

                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Tax:" FontSize="14"/>
                        <TextBlock Grid.Column="1" Text="{x:Bind CartService.FormattedTaxAmount, Mode=OneWay}" FontSize="14"/>
                    </Grid>

                    <Border Height="1" Background="{ThemeResource SystemControlForegroundBaseLowBrush}" Margin="0,8"/>

                    <Grid Margin="0,8,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Total:" FontSize="18" FontWeight="Bold"/>
                        <TextBlock Grid.Column="1" Text="{x:Bind CartService.FormattedTotal, Mode=OneWay}" FontSize="18" FontWeight="Bold"/>
                    </Grid>

                    <!-- Transaction Status -->
                    <Border x:Name="TransactionStatusBorder"
                            Background="{ThemeResource SystemControlBackgroundBaseLowBrush}"
                            CornerRadius="4"
                            Padding="12"
                            Visibility="Collapsed">
                        <StackPanel>
                            <TextBlock x:Name="TransactionStatusText" 
                                       Text="Processing..."
                                       FontWeight="SemiBold"
                                       HorizontalAlignment="Center"/>
                            <ProgressBar x:Name="TransactionProgressBar" 
                                         IsIndeterminate="True"
                                         Margin="0,8,0,0"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Page>
