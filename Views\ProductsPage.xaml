<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="firstapp.Views.ProductsPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:firstapp.Views"
    xmlns:models="using:firstapp.Models"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid Padding="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <TextBlock Grid.Row="0" 
                   Text="Product Catalog" 
                   Style="{StaticResource TitleTextBlockStyle}" 
                   Margin="0,0,0,20"/>

        <!-- Search and Filter Controls -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="200"/>
            </Grid.ColumnDefinitions>

            <!-- Search Box -->
            <TextBox x:Name="SearchBox" 
                     Grid.Column="0"
                     PlaceholderText="Search products..."
                     TextChanged="SearchBox_TextChanged"
                     Margin="0,0,10,0"
                     Height="40"/>

            <!-- Search Button -->
            <Button Grid.Column="1" 
                    Content="Search" 
                    Click="SearchButton_Click"
                    Height="40"
                    Width="80"
                    Margin="0,0,10,0"/>

            <!-- Category Filter -->
            <ComboBox x:Name="CategoryComboBox"
                      Grid.Column="2"
                      PlaceholderText="All Categories"
                      SelectionChanged="CategoryComboBox_SelectionChanged"
                      Height="40"/>
        </Grid>

        <!-- Products Grid -->
        <ScrollViewer Grid.Row="2" ZoomMode="Disabled">
            <GridView x:Name="ProductsGridView"
                      ItemsSource="{x:Bind FilteredProducts, Mode=OneWay}"
                      SelectionMode="None"
                      IsItemClickEnabled="True"
                      ItemClick="ProductsGridView_ItemClick">
                
                <GridView.ItemsPanel>
                    <ItemsPanelTemplate>
                        <ItemsWrapGrid Orientation="Horizontal" 
                                       ItemWidth="200" 
                                       ItemHeight="280"/>
                    </ItemsPanelTemplate>
                </GridView.ItemsPanel>

                <GridView.ItemTemplate>
                    <DataTemplate x:DataType="models:Product">
                        <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                                CornerRadius="8"
                                Padding="12"
                                Margin="8">
                            <Border.Shadow>
                                <ThemeShadow />
                            </Border.Shadow>

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="120"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>

                                <!-- Product Image Placeholder -->
                                <Border Grid.Row="0" 
                                        Background="{ThemeResource SystemControlBackgroundBaseLowBrush}"
                                        CornerRadius="4"
                                        Margin="0,0,0,8">
                                    <FontIcon Glyph="&#xE7C3;" 
                                              FontSize="48" 
                                              Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"/>
                                </Border>

                                <!-- Product Name -->
                                <TextBlock Grid.Row="1" 
                                           Text="{x:Bind Name}" 
                                           FontWeight="SemiBold"
                                           TextWrapping="Wrap"
                                           MaxLines="2"
                                           Margin="0,0,0,4"/>

                                <!-- Category -->
                                <TextBlock Grid.Row="2" 
                                           Text="{x:Bind Category}" 
                                           FontSize="12"
                                           Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"
                                           Margin="0,0,0,4"/>

                                <!-- Price -->
                                <TextBlock Grid.Row="3" 
                                           Text="{x:Bind FormattedPrice}" 
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Foreground="{ThemeResource SystemAccentColor}"
                                           Margin="0,0,0,8"/>

                                <!-- Stock Status -->
                                <StackPanel Grid.Row="4" Orientation="Horizontal" Margin="0,0,0,8">
                                    <Ellipse Width="8" Height="8"
                                             Fill="Green"
                                             VerticalAlignment="Center"
                                             Margin="0,0,4,0"/>
                                    <TextBlock Text="{x:Bind StockQuantity}"
                                               FontSize="12"
                                               Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"/>
                                </StackPanel>

                                <!-- Add to Cart Button -->
                                <Button Grid.Row="5" 
                                        Content="Add to Cart"
                                        HorizontalAlignment="Stretch"
                                        IsEnabled="{x:Bind IsInStock}"
                                        Click="AddToCartButton_Click"
                                        Tag="{x:Bind}"
                                        Style="{StaticResource AccentButtonStyle}"/>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </GridView.ItemTemplate>
            </GridView>
        </ScrollViewer>
    </Grid>
</Page>
