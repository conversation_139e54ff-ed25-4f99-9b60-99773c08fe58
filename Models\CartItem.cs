using System;
using System.ComponentModel;

namespace firstapp.Models
{
    public class CartItem : INotifyPropertyChanged
    {
        private int _quantity;
        private Product _product;

        public CartItem(Product product, int quantity = 1)
        {
            _product = product ?? throw new ArgumentNullException(nameof(product));
            _quantity = quantity;
        }

        public Product Product
        {
            get => _product;
            set
            {
                _product = value;
                OnPropertyChanged(nameof(Product));
                OnPropertyChanged(nameof(Subtotal));
                OnPropertyChanged(nameof(FormattedSubtotal));
            }
        }

        public int Quantity
        {
            get => _quantity;
            set
            {
                _quantity = Math.Max(0, value);
                OnPropertyChanged(nameof(Quantity));
                OnPropertyChanged(nameof(Subtotal));
                OnPropertyChanged(nameof(FormattedSubtotal));
            }
        }

        // Computed properties
        public decimal Subtotal => Product.Price * Quantity;
        public string FormattedSubtotal => $"${Subtotal:F2}";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
