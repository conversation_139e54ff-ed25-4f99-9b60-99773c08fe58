using System;
using System.Linq;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Navigation;
using firstapp.Services;

namespace firstapp.Views
{
    public sealed partial class SettingsPage : Page
    {
        private CartService? _cartService;

        public SettingsPage()
        {
            InitializeComponent();
        }

        protected override void OnNavigatedTo(NavigationEventArgs e)
        {
            base.OnNavigatedTo(e);

            if (e.Parameter is object param)
            {
                var properties = param.GetType().GetProperties();
                var cartServiceProp = properties.FirstOrDefault(p => p.PropertyType == typeof(CartService));

                if (cartServiceProp != null)
                {
                    _cartService = (CartService?)cartServiceProp.GetValue(param);
                    
                    // Set current tax rate
                    if (_cartService != null)
                    {
                        TaxRateNumberBox.Value = (double)(_cartService.TaxRate * 100);
                    }
                }
            }
        }

        private void TaxRateNumberBox_ValueChanged(NumberBox sender, NumberBoxValueChangedEventArgs args)
        {
            if (_cartService != null && !double.IsNaN(args.NewValue))
            {
                _cartService.TaxRate = (decimal)(args.NewValue / 100);
            }
        }

        private async void SaveSettingsButton_Click(object sender, RoutedEventArgs e)
        {
            // In a real application, settings would be saved to local storage or a database
            var dialog = new ContentDialog
            {
                Title = "Settings Saved",
                Content = "Your settings have been saved successfully.",
                PrimaryButtonText = "OK",
                XamlRoot = this.XamlRoot
            };

            await dialog.ShowAsync();
        }

        private async void ResetToDefaultsButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ContentDialog
            {
                Title = "Reset Settings",
                Content = "Are you sure you want to reset all settings to their default values?",
                PrimaryButtonText = "Reset",
                SecondaryButtonText = "Cancel",
                DefaultButton = ContentDialogButton.Secondary,
                XamlRoot = this.XamlRoot
            };

            var result = await dialog.ShowAsync();
            if (result == ContentDialogResult.Primary)
            {
                ResetToDefaults();
            }
        }

        private void ResetToDefaults()
        {
            // Reset store information
            StoreNameTextBox.Text = "Retail POS System";
            StoreAddressTextBox.Text = "123 Main Street";
            StoreCityTextBox.Text = "City";
            StoreStateTextBox.Text = "State";
            StoreZipTextBox.Text = "12345";
            StorePhoneTextBox.Text = "(*************";

            // Reset tax rate
            TaxRateNumberBox.Value = 8.0;
            if (_cartService != null)
            {
                _cartService.TaxRate = 0.08m;
            }

            // Reset receipt settings
            AutoPrintReceiptCheckBox.IsChecked = false;
            ShowReceiptPreviewCheckBox.IsChecked = true;
            EmailReceiptOptionCheckBox.IsChecked = true;

            // Reset display settings
            ThemeComboBox.SelectedIndex = 0;
            FontSizeSlider.Value = 14;
        }
    }
}
