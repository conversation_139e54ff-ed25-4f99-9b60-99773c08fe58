using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Navigation;
using firstapp.Models;
using firstapp.Services;
using System.Collections.Generic;

namespace firstapp.Views
{
    public sealed partial class CheckoutPage : Page
    {
        private CartService? _cartService;
        private Customer _currentCustomer = new();

        public CartService? CartService => _cartService;

        public CheckoutPage()
        {
            InitializeComponent();
        }

        protected override void OnNavigatedTo(NavigationEventArgs e)
        {
            base.OnNavigatedTo(e);

            if (e.Parameter is object param)
            {
                var properties = param.GetType().GetProperties();
                var cartServiceProp = properties.FirstOrDefault(p => p.PropertyType == typeof(CartService));

                if (cartServiceProp != null)
                {
                    _cartService = (CartService?)cartServiceProp.GetValue(param);
                }
            }

            // Check if cart is empty and redirect if necessary
            if (_cartService?.Items.Count == 0)
            {
                ShowEmptyCartMessage();
            }
        }

        private async void ShowEmptyCartMessage()
        {
            var dialog = new ContentDialog
            {
                Title = "Empty Cart",
                Content = "Your cart is empty. Please add some items before proceeding to checkout.",
                PrimaryButtonText = "Go to Products",
                XamlRoot = this.XamlRoot
            };

            await dialog.ShowAsync();
            
            // Navigate back to products
            if (Frame.Parent is NavigationView navView)
            {
                var productsItem = navView.MenuItems.OfType<NavigationViewItem>()
                    .FirstOrDefault(item => item.Tag?.ToString() == "Products");
                
                if (productsItem != null)
                {
                    navView.SelectedItem = productsItem;
                }
            }
        }

        private void DiscountTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // Real-time validation could be added here
        }

        private void ApplyDiscountButton_Click(object sender, RoutedEventArgs e)
        {
            if (_cartService != null && decimal.TryParse(DiscountTextBox.Text, out decimal discountAmount))
            {
                _cartService.DiscountAmount = Math.Max(0, discountAmount);
                DiscountGrid.Visibility = discountAmount > 0 ? Visibility.Visible : Visibility.Collapsed;
                
                // Show confirmation
                ShowDiscountAppliedNotification(discountAmount);
            }
            else
            {
                ShowInvalidDiscountNotification();
            }
        }

        private void ShowDiscountAppliedNotification(decimal amount)
        {
            var infoBar = new InfoBar
            {
                Title = "Discount Applied",
                Message = $"Discount of ${amount:F2} has been applied to your order.",
                Severity = InfoBarSeverity.Success,
                IsOpen = true
            };

            AddTemporaryNotification(infoBar);
        }

        private void ShowInvalidDiscountNotification()
        {
            var infoBar = new InfoBar
            {
                Title = "Invalid Discount",
                Message = "Please enter a valid discount amount.",
                Severity = InfoBarSeverity.Warning,
                IsOpen = true
            };

            AddTemporaryNotification(infoBar);
        }

        private void AddTemporaryNotification(InfoBar infoBar)
        {
            if (Content is ScrollViewer scrollViewer && scrollViewer.Content is Grid mainGrid)
            {
                mainGrid.Children.Add(infoBar);
                Grid.SetRow(infoBar, 0);
                Grid.SetColumnSpan(infoBar, 3);

                // Auto-hide after 3 seconds
                var timer = new DispatcherTimer { Interval = TimeSpan.FromSeconds(3) };
                timer.Tick += (s, e) =>
                {
                    timer.Stop();
                    mainGrid.Children.Remove(infoBar);
                };
                timer.Start();
            }
        }

        private void ProceedToPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            if (_cartService?.Items.Count == 0)
            {
                ShowEmptyCartMessage();
                return;
            }

            // Validate customer information
            if (!ValidateCustomerInformation())
            {
                return;
            }

            // Update customer information
            UpdateCustomerFromForm();

            // Get selected payment method
            var paymentMethod = GetSelectedPaymentMethod();

            // Navigate to payment page
            if (Frame.Parent is NavigationView navView)
            {
                var paymentItem = navView.MenuItems.OfType<NavigationViewItem>()
                    .FirstOrDefault(item => item.Tag?.ToString() == "Payment");
                
                if (paymentItem != null)
                {
                    navView.SelectedItem = paymentItem;
                }
            }
        }

        private bool ValidateCustomerInformation()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(FirstNameTextBox.Text))
                errors.Add("First name is required");

            if (string.IsNullOrWhiteSpace(LastNameTextBox.Text))
                errors.Add("Last name is required");

            if (!string.IsNullOrWhiteSpace(EmailTextBox.Text) && !IsValidEmail(EmailTextBox.Text))
                errors.Add("Please enter a valid email address");

            if (errors.Any())
            {
                ShowValidationErrors(errors);
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private async void ShowValidationErrors(List<string> errors)
        {
            var dialog = new ContentDialog
            {
                Title = "Validation Errors",
                Content = string.Join("\n", errors),
                PrimaryButtonText = "OK",
                XamlRoot = this.XamlRoot
            };

            await dialog.ShowAsync();
        }

        private void UpdateCustomerFromForm()
        {
            _currentCustomer.FirstName = FirstNameTextBox.Text;
            _currentCustomer.LastName = LastNameTextBox.Text;
            _currentCustomer.Email = EmailTextBox.Text;
            _currentCustomer.PhoneNumber = PhoneTextBox.Text;
            _currentCustomer.Address = AddressTextBox.Text;
        }

        private PaymentMethod GetSelectedPaymentMethod()
        {
            if (CashRadioButton.IsChecked == true) return PaymentMethod.Cash;
            if (CreditCardRadioButton.IsChecked == true) return PaymentMethod.CreditCard;
            if (DebitCardRadioButton.IsChecked == true) return PaymentMethod.DebitCard;
            if (DigitalWalletRadioButton.IsChecked == true) return PaymentMethod.DigitalWallet;
            if (GiftCardRadioButton.IsChecked == true) return PaymentMethod.GiftCard;
            
            return PaymentMethod.Cash; // Default
        }
    }
}
