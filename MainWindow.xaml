<?xml version="1.0" encoding="utf-8"?>
<Window
    x:Class="firstapp.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:firstapp"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d"
    Title="Retail POS System">

    <Window.SystemBackdrop>
        <MicaBackdrop />
    </Window.SystemBackdrop>

    <Grid>
        <NavigationView x:Name="MainNavigationView"
                       IsBackButtonVisible="Collapsed"
                       IsSettingsVisible="False"
                       PaneDisplayMode="Left"
                       OpenPaneLength="200"
                       CompactModeThresholdWidth="0"
                       ExpandedModeThresholdWidth="0"
                       SelectionChanged="NavigationView_SelectionChanged">

            <NavigationView.MenuItems>
                <NavigationViewItem Icon="Shop" Content="Products" Tag="Products" />
                <NavigationViewItem Icon="ShoppingCart" Content="Cart" Tag="Cart" />
                <NavigationViewItem Icon="ContactInfo" Content="Checkout" Tag="Checkout" />
                <NavigationViewItem Icon="CreditCard" Content="Payment" Tag="Payment" />
                <NavigationViewItem Icon="Document" Content="Receipt" Tag="Receipt" />
            </NavigationView.MenuItems>

            <NavigationView.FooterMenuItems>
                <NavigationViewItem Icon="Setting" Content="Settings" Tag="Settings" />
            </NavigationView.FooterMenuItems>

            <Frame x:Name="ContentFrame" />
        </NavigationView>
    </Grid>
</Window>
