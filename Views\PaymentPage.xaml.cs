using System;
using System.Linq;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Navigation;
using firstapp.Models;
using firstapp.Services;

namespace firstapp.Views
{
    public sealed partial class PaymentPage : Page
    {
        private CartService? _cartService;
        private PaymentMethod _currentPaymentMethod = PaymentMethod.Cash;
        private decimal _cashReceived = 0;
        private bool _paymentProcessed = false;

        public CartService? CartService => _cartService;
        public Visibility HasDiscount => _cartService?.DiscountAmount > 0 ? Visibility.Visible : Visibility.Collapsed;

        public PaymentPage()
        {
            InitializeComponent();
        }

        protected override void OnNavigatedTo(NavigationEventArgs e)
        {
            base.OnNavigatedTo(e);

            if (e.Parameter is object param)
            {
                var properties = param.GetType().GetProperties();
                var cartServiceProp = properties.FirstOrDefault(p => p.PropertyType == typeof(CartService));

                if (cartServiceProp != null)
                {
                    _cartService = (CartService?)cartServiceProp.GetValue(param);
                }
            }

            // Check if cart is empty and redirect if necessary
            if (_cartService?.Items.Count == 0)
            {
                ShowEmptyCartMessage();
                return;
            }

            UpdatePaymentMethodDisplay();
            UpdateDiscountVisibility();
        }

        private async void ShowEmptyCartMessage()
        {
            var dialog = new ContentDialog
            {
                Title = "Empty Cart",
                Content = "Your cart is empty. Please add some items before proceeding to payment.",
                PrimaryButtonText = "Go to Products",
                XamlRoot = this.XamlRoot
            };

            await dialog.ShowAsync();
            NavigateToProducts();
        }

        private void NavigateToProducts()
        {
            if (Frame.Parent is NavigationView navView)
            {
                var productsItem = navView.MenuItems.OfType<NavigationViewItem>()
                    .FirstOrDefault(item => item.Tag?.ToString() == "Products");
                
                if (productsItem != null)
                {
                    navView.SelectedItem = productsItem;
                }
            }
        }

        private void UpdatePaymentMethodDisplay()
        {
            switch (_currentPaymentMethod)
            {
                case PaymentMethod.Cash:
                    PaymentMethodIcon.Glyph = "\uE8C7";
                    PaymentMethodText.Text = "Cash Payment";
                    ShowCashPaymentPanel();
                    break;
                case PaymentMethod.CreditCard:
                case PaymentMethod.DebitCard:
                    PaymentMethodIcon.Glyph = "\uE8C7";
                    PaymentMethodText.Text = _currentPaymentMethod == PaymentMethod.CreditCard ? "Credit Card" : "Debit Card";
                    ShowCardPaymentPanel();
                    break;
                case PaymentMethod.DigitalWallet:
                    PaymentMethodIcon.Glyph = "\uE704";
                    PaymentMethodText.Text = "Digital Wallet";
                    ShowDigitalWalletPanel();
                    break;
                case PaymentMethod.GiftCard:
                    PaymentMethodIcon.Glyph = "\uE8C7";
                    PaymentMethodText.Text = "Gift Card";
                    ShowCardPaymentPanel();
                    break;
            }
        }

        private void ShowCashPaymentPanel()
        {
            CashPaymentPanel.Visibility = Visibility.Visible;
            CardPaymentPanel.Visibility = Visibility.Collapsed;
            DigitalWalletPanel.Visibility = Visibility.Collapsed;
        }

        private void ShowCardPaymentPanel()
        {
            CashPaymentPanel.Visibility = Visibility.Collapsed;
            CardPaymentPanel.Visibility = Visibility.Visible;
            DigitalWalletPanel.Visibility = Visibility.Collapsed;
        }

        private void ShowDigitalWalletPanel()
        {
            CashPaymentPanel.Visibility = Visibility.Collapsed;
            CardPaymentPanel.Visibility = Visibility.Collapsed;
            DigitalWalletPanel.Visibility = Visibility.Visible;
        }

        private void UpdateDiscountVisibility()
        {
            TransactionDiscountGrid.Visibility = _cartService?.DiscountAmount > 0 ? Visibility.Visible : Visibility.Collapsed;
        }

        private void ChangePaymentMethodButton_Click(object sender, RoutedEventArgs e)
        {
            ShowPaymentMethodSelection();
        }

        private async void ShowPaymentMethodSelection()
        {
            var dialog = new ContentDialog
            {
                Title = "Select Payment Method",
                PrimaryButtonText = "OK",
                SecondaryButtonText = "Cancel",
                XamlRoot = this.XamlRoot
            };

            var stackPanel = new StackPanel();
            var cashRadio = new RadioButton { Content = "Cash", GroupName = "PaymentMethod", IsChecked = _currentPaymentMethod == PaymentMethod.Cash };
            var creditRadio = new RadioButton { Content = "Credit Card", GroupName = "PaymentMethod", IsChecked = _currentPaymentMethod == PaymentMethod.CreditCard };
            var debitRadio = new RadioButton { Content = "Debit Card", GroupName = "PaymentMethod", IsChecked = _currentPaymentMethod == PaymentMethod.DebitCard };
            var walletRadio = new RadioButton { Content = "Digital Wallet", GroupName = "PaymentMethod", IsChecked = _currentPaymentMethod == PaymentMethod.DigitalWallet };
            var giftRadio = new RadioButton { Content = "Gift Card", GroupName = "PaymentMethod", IsChecked = _currentPaymentMethod == PaymentMethod.GiftCard };

            stackPanel.Children.Add(cashRadio);
            stackPanel.Children.Add(creditRadio);
            stackPanel.Children.Add(debitRadio);
            stackPanel.Children.Add(walletRadio);
            stackPanel.Children.Add(giftRadio);

            dialog.Content = stackPanel;

            var result = await dialog.ShowAsync();
            if (result == ContentDialogResult.Primary)
            {
                if (cashRadio.IsChecked == true) _currentPaymentMethod = PaymentMethod.Cash;
                else if (creditRadio.IsChecked == true) _currentPaymentMethod = PaymentMethod.CreditCard;
                else if (debitRadio.IsChecked == true) _currentPaymentMethod = PaymentMethod.DebitCard;
                else if (walletRadio.IsChecked == true) _currentPaymentMethod = PaymentMethod.DigitalWallet;
                else if (giftRadio.IsChecked == true) _currentPaymentMethod = PaymentMethod.GiftCard;

                UpdatePaymentMethodDisplay();
                UpdateProcessButtonState();
            }
        }

        private void CashReceivedTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (decimal.TryParse(CashReceivedTextBox.Text, out decimal received))
            {
                _cashReceived = received;
                var total = _cartService?.Total ?? 0;
                var change = _cashReceived - total;

                if (change >= 0)
                {
                    ChangeDueText.Text = $"${change:F2}";
                    ChangePanel.Visibility = Visibility.Visible;
                }
                else
                {
                    ChangePanel.Visibility = Visibility.Collapsed;
                }
            }
            else
            {
                _cashReceived = 0;
                ChangePanel.Visibility = Visibility.Collapsed;
            }

            UpdateProcessButtonState();
        }

        private void UpdateProcessButtonState()
        {
            bool canProcess = false;

            switch (_currentPaymentMethod)
            {
                case PaymentMethod.Cash:
                    canProcess = _cashReceived >= (_cartService?.Total ?? 0);
                    break;
                case PaymentMethod.CreditCard:
                case PaymentMethod.DebitCard:
                case PaymentMethod.DigitalWallet:
                case PaymentMethod.GiftCard:
                    canProcess = true; // For simulation purposes
                    break;
            }

            ProcessPaymentButton.IsEnabled = canProcess && !_paymentProcessed;
        }

        private async void SimulateCardButton_Click(object sender, RoutedEventArgs e)
        {
            CardProcessingRing.IsActive = true;
            SimulateCardButton.IsEnabled = false;

            // Simulate card processing delay
            await System.Threading.Tasks.Task.Delay(2000);

            CardProcessingRing.IsActive = false;
            SimulateCardButton.IsEnabled = true;
            UpdateProcessButtonState();
        }

        private async void SimulateWalletButton_Click(object sender, RoutedEventArgs e)
        {
            WalletProcessingRing.IsActive = true;
            SimulateWalletButton.IsEnabled = false;

            // Simulate wallet processing delay
            await System.Threading.Tasks.Task.Delay(1500);

            WalletProcessingRing.IsActive = false;
            SimulateWalletButton.IsEnabled = true;
            UpdateProcessButtonState();
        }

        private async void ProcessPaymentButton_Click(object sender, RoutedEventArgs e)
        {
            if (_cartService == null) return;

            _paymentProcessed = true;
            ProcessPaymentButton.IsEnabled = false;

            // Show processing status
            TransactionStatusBorder.Visibility = Visibility.Visible;
            TransactionStatusText.Text = "Processing payment...";
            TransactionProgressBar.IsIndeterminate = true;

            // Simulate payment processing
            await System.Threading.Tasks.Task.Delay(3000);

            // Complete the transaction
            var transaction = _cartService.CreateTransaction(null, _currentPaymentMethod);
            transaction.Status = TransactionStatus.Completed;

            // Update status
            TransactionStatusText.Text = "Payment successful!";
            TransactionProgressBar.IsIndeterminate = false;
            TransactionProgressBar.Value = 100;

            // Navigate to receipt page after a short delay
            await System.Threading.Tasks.Task.Delay(1000);
            NavigateToReceipt();
        }

        private void NavigateToReceipt()
        {
            if (Frame.Parent is NavigationView navView)
            {
                var receiptItem = navView.MenuItems.OfType<NavigationViewItem>()
                    .FirstOrDefault(item => item.Tag?.ToString() == "Receipt");
                
                if (receiptItem != null)
                {
                    navView.SelectedItem = receiptItem;
                }
            }
        }

        private async void CancelTransactionButton_Click(object sender, RoutedEventArgs e)
        {
            var dialog = new ContentDialog
            {
                Title = "Cancel Transaction",
                Content = "Are you sure you want to cancel this transaction?",
                PrimaryButtonText = "Yes, Cancel",
                SecondaryButtonText = "No",
                DefaultButton = ContentDialogButton.Secondary,
                XamlRoot = this.XamlRoot
            };

            var result = await dialog.ShowAsync();
            if (result == ContentDialogResult.Primary)
            {
                NavigateToProducts();
            }
        }
    }
}
