using System;
using System.Linq;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Navigation;
using firstapp.Models;
using firstapp.Services;

namespace firstapp.Views
{
    public sealed partial class ReceiptPage : Page
    {
        private CartService? _cartService;
        private Transaction? _currentTransaction;

        public ReceiptPage()
        {
            InitializeComponent();
        }

        protected override void OnNavigatedTo(NavigationEventArgs e)
        {
            base.OnNavigatedTo(e);

            if (e.Parameter is object param)
            {
                var properties = param.GetType().GetProperties();
                var cartServiceProp = properties.FirstOrDefault(p => p.PropertyType == typeof(CartService));

                if (cartServiceProp != null)
                {
                    _cartService = (CartService?)cartServiceProp.GetValue(param);
                }
            }

            if (_cartService != null)
            {
                GenerateReceipt();
            }
        }

        private void GenerateReceipt()
        {
            if (_cartService == null) return;

            // Create transaction from current cart
            _currentTransaction = _cartService.CreateTransaction();
            _currentTransaction.Status = TransactionStatus.Completed;

            // Populate receipt data
            PopulateReceiptData();
        }

        private void PopulateReceiptData()
        {
            if (_currentTransaction == null) return;

            // Transaction details
            TransactionNumberText.Text = _currentTransaction.TransactionNumber;
            TransactionDateText.Text = _currentTransaction.Timestamp.ToString("MM/dd/yyyy");
            TransactionTimeText.Text = _currentTransaction.Timestamp.ToString("HH:mm:ss");
            PaymentMethodText.Text = GetPaymentMethodDisplayName(_currentTransaction.PaymentMethod);

            // Items
            ReceiptItemsControl.ItemsSource = _currentTransaction.Items;

            // Totals
            SubtotalText.Text = _currentTransaction.FormattedSubtotal;
            TaxText.Text = _currentTransaction.FormattedTaxAmount;
            TotalText.Text = _currentTransaction.FormattedTotal;

            // Discount (if applicable)
            if (_currentTransaction.DiscountAmount > 0)
            {
                DiscountRow.Visibility = Visibility.Visible;
                DiscountText.Text = $"-{_currentTransaction.FormattedDiscountAmount}";
            }

            // Cash payment details (if cash payment)
            if (_currentTransaction.PaymentMethod == PaymentMethod.Cash)
            {
                ShowCashPaymentDetails();
            }
        }

        private void ShowCashPaymentDetails()
        {
            // For demonstration, we'll simulate cash received and change
            // In a real implementation, this would come from the payment processing
            var total = _currentTransaction?.Total ?? 0;
            var cashReceived = Math.Ceiling(total); // Simulate cash received (rounded up)
            var change = cashReceived - total;

            CashReceivedText.Text = $"${cashReceived:F2}";
            ChangeText.Text = $"${change:F2}";
            CashPaymentDetails.Visibility = Visibility.Visible;
        }

        private string GetPaymentMethodDisplayName(PaymentMethod paymentMethod)
        {
            return paymentMethod switch
            {
                PaymentMethod.Cash => "Cash",
                PaymentMethod.CreditCard => "Credit Card",
                PaymentMethod.DebitCard => "Debit Card",
                PaymentMethod.DigitalWallet => "Digital Wallet",
                PaymentMethod.GiftCard => "Gift Card",
                _ => "Unknown"
            };
        }

        private async void PrintReceiptButton_Click(object sender, RoutedEventArgs e)
        {
            // In a real implementation, this would integrate with a printer
            var dialog = new ContentDialog
            {
                Title = "Print Receipt",
                Content = "Receipt printing functionality would be implemented here.\n\nThis would typically integrate with a thermal receipt printer or standard printer.",
                PrimaryButtonText = "OK",
                XamlRoot = this.XamlRoot
            };

            await dialog.ShowAsync();
        }

        private async void EmailReceiptButton_Click(object sender, RoutedEventArgs e)
        {
            // Show email input dialog
            var emailDialog = new ContentDialog
            {
                Title = "Email Receipt",
                PrimaryButtonText = "Send",
                SecondaryButtonText = "Cancel",
                XamlRoot = this.XamlRoot
            };

            var stackPanel = new StackPanel();
            stackPanel.Children.Add(new TextBlock { Text = "Enter email address:", Margin = new Thickness(0, 0, 0, 8) });
            
            var emailTextBox = new TextBox 
            { 
                PlaceholderText = "<EMAIL>",
                InputScope = new InputScope()
            };
            emailTextBox.InputScope.Names.Add(new InputScopeName(InputScopeNameValue.EmailNameOrAddress));
            stackPanel.Children.Add(emailTextBox);

            emailDialog.Content = stackPanel;

            var result = await emailDialog.ShowAsync();
            if (result == ContentDialogResult.Primary && !string.IsNullOrWhiteSpace(emailTextBox.Text))
            {
                await SendEmailReceipt(emailTextBox.Text);
            }
        }

        private async System.Threading.Tasks.Task SendEmailReceipt(string emailAddress)
        {
            // Simulate email sending
            var progressDialog = new ContentDialog
            {
                Title = "Sending Email",
                Content = "Sending receipt to " + emailAddress + "...",
                XamlRoot = this.XamlRoot
            };

            var progressRing = new ProgressRing { IsActive = true, Width = 50, Height = 50 };
            progressDialog.Content = new StackPanel
            {
                Children = 
                {
                    new TextBlock { Text = "Sending receipt to " + emailAddress + "...", Margin = new Thickness(0, 0, 0, 16) },
                    progressRing
                }
            };

            var showTask = progressDialog.ShowAsync();

            // Simulate email processing delay
            await System.Threading.Tasks.Task.Delay(2000);

            progressDialog.Hide();

            // Show success message
            var successDialog = new ContentDialog
            {
                Title = "Email Sent",
                Content = $"Receipt has been successfully sent to {emailAddress}",
                PrimaryButtonText = "OK",
                XamlRoot = this.XamlRoot
            };

            await successDialog.ShowAsync();
        }

        private void NewTransactionButton_Click(object sender, RoutedEventArgs e)
        {
            // Clear the cart and start a new transaction
            _cartService?.Clear();
            NavigateToProducts();
        }

        private void BackToProductsButton_Click(object sender, RoutedEventArgs e)
        {
            NavigateToProducts();
        }

        private void NavigateToProducts()
        {
            if (Frame.Parent is NavigationView navView)
            {
                var productsItem = navView.MenuItems.OfType<NavigationViewItem>()
                    .FirstOrDefault(item => item.Tag?.ToString() == "Products");
                
                if (productsItem != null)
                {
                    navView.SelectedItem = productsItem;
                }
            }
        }
    }
}
