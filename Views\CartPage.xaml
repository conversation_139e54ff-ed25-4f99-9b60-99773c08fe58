<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="firstapp.Views.CartPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:firstapp.Views"
    xmlns:models="using:firstapp.Models"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid Padding="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0"
                       Text="Shopping Cart" 
                       Style="{StaticResource TitleTextBlockStyle}"/>
            
            <Button Grid.Column="1"
                    Content="Clear Cart"
                    Click="ClearCartButton_Click"
                    Style="{StaticResource DefaultButtonStyle}"/>
        </Grid>

        <!-- Cart Items or Empty State -->
        <Grid Grid.Row="1">
            <!-- Empty Cart State -->
            <StackPanel x:Name="EmptyCartPanel"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center"
                        Visibility="{x:Bind IsCartEmpty, Mode=OneWay}">
                <FontIcon Glyph="&#xE7BF;" 
                          FontSize="64" 
                          Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"
                          HorizontalAlignment="Center"
                          Margin="0,0,0,16"/>
                <TextBlock Text="Your cart is empty" 
                           Style="{StaticResource SubtitleTextBlockStyle}"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,8"/>
                <TextBlock Text="Add some products to get started" 
                           Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"
                           HorizontalAlignment="Center"/>
            </StackPanel>

            <!-- Cart Items List -->
            <ListView x:Name="CartItemsListView"
                      ItemsSource="{x:Bind CartService.Items, Mode=OneWay}"
                      SelectionMode="None"
                      Visibility="{x:Bind IsCartNotEmpty, Mode=OneWay}">
                
                <ListView.ItemTemplate>
                    <DataTemplate x:DataType="models:CartItem">
                        <Border Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                                CornerRadius="8"
                                Padding="16"
                                Margin="0,4">
                            <Border.Shadow>
                                <ThemeShadow />
                            </Border.Shadow>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <!-- Product Image Placeholder -->
                                <Border Grid.Column="0" 
                                        Background="{ThemeResource SystemControlBackgroundBaseLowBrush}"
                                        CornerRadius="4"
                                        Width="64"
                                        Height="64"
                                        Margin="0,0,16,0">
                                    <FontIcon Glyph="&#xE7C3;" 
                                              FontSize="24" 
                                              Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"/>
                                </Border>

                                <!-- Product Details -->
                                <StackPanel Grid.Column="1" VerticalAlignment="Center">
                                    <TextBlock Text="{x:Bind Product.Name}" 
                                               FontWeight="SemiBold"
                                               TextWrapping="Wrap"/>
                                    <TextBlock Text="{x:Bind Product.Category}" 
                                               FontSize="12"
                                               Foreground="{ThemeResource SystemControlForegroundBaseMediumBrush}"
                                               Margin="0,2,0,0"/>
                                    <TextBlock Text="{x:Bind Product.FormattedPrice}" 
                                               FontSize="14"
                                               Foreground="{ThemeResource SystemAccentColor}"
                                               Margin="0,4,0,0"/>
                                </StackPanel>

                                <!-- Quantity Controls -->
                                <StackPanel Grid.Column="2" 
                                            Orientation="Horizontal" 
                                            VerticalAlignment="Center"
                                            Margin="16,0">
                                    <Button Content="−" 
                                            Width="40" 
                                            Height="40"
                                            Click="DecreaseQuantityButton_Click"
                                            Tag="{x:Bind}"
                                            FontSize="16"/>
                                    <TextBlock Text="{x:Bind Quantity, Mode=OneWay}" 
                                               VerticalAlignment="Center"
                                               HorizontalAlignment="Center"
                                               Width="40"
                                               TextAlignment="Center"
                                               FontSize="16"
                                               FontWeight="SemiBold"/>
                                    <Button Content="+" 
                                            Width="40" 
                                            Height="40"
                                            Click="IncreaseQuantityButton_Click"
                                            Tag="{x:Bind}"
                                            FontSize="16"/>
                                </StackPanel>

                                <!-- Subtotal -->
                                <TextBlock Grid.Column="3" 
                                           Text="{x:Bind FormattedSubtotal, Mode=OneWay}" 
                                           VerticalAlignment="Center"
                                           FontSize="16"
                                           FontWeight="Bold"
                                           Margin="16,0"/>

                                <!-- Remove Button -->
                                <Button Grid.Column="4" 
                                        Content="🗑️" 
                                        Width="40" 
                                        Height="40"
                                        Click="RemoveItemButton_Click"
                                        Tag="{x:Bind}"
                                        ToolTipService.ToolTip="Remove item"/>
                            </Grid>
                        </Border>
                    </DataTemplate>
                </ListView.ItemTemplate>
            </ListView>
        </Grid>

        <!-- Cart Summary -->
        <Border Grid.Row="2" 
                Background="{ThemeResource CardBackgroundFillColorDefaultBrush}"
                CornerRadius="8"
                Padding="20"
                Margin="0,20,0,0"
                Visibility="{x:Bind IsCartNotEmpty, Mode=OneWay}">
            <Border.Shadow>
                <ThemeShadow />
            </Border.Shadow>

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="200"/>
                </Grid.ColumnDefinitions>

                <!-- Summary Details -->
                <StackPanel Grid.Column="0">
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Subtotal:" FontSize="14"/>
                        <TextBlock Grid.Column="1" Text="{x:Bind CartService.FormattedSubtotal, Mode=OneWay}" FontSize="14"/>
                    </Grid>

                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Tax:" FontSize="14"/>
                        <TextBlock Grid.Column="1" Text="{x:Bind CartService.FormattedTaxAmount, Mode=OneWay}" FontSize="14"/>
                    </Grid>

                    <Border Height="1" Background="{ThemeResource SystemControlForegroundBaseLowBrush}" Margin="0,8"/>

                    <Grid Margin="0,8,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Total:" FontSize="18" FontWeight="Bold"/>
                        <TextBlock Grid.Column="1" Text="{x:Bind CartService.FormattedTotal, Mode=OneWay}" FontSize="18" FontWeight="Bold"/>
                    </Grid>
                </StackPanel>

                <!-- Checkout Button -->
                <Button Grid.Column="1" 
                        Content="Proceed to Checkout"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Bottom"
                        Style="{StaticResource AccentButtonStyle}"
                        Click="CheckoutButton_Click"
                        Height="48"/>
            </Grid>
        </Border>
    </Grid>
</Page>
