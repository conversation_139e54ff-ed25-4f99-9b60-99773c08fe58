using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using firstapp.Models;

namespace firstapp.Services
{
    public class CartService : INotifyPropertyChanged
    {
        private readonly ObservableCollection<CartItem> _items = new();
        private decimal _taxRate = 0.08m;
        private decimal _discountAmount = 0;

        public ObservableCollection<CartItem> Items => _items;

        public decimal TaxRate
        {
            get => _taxRate;
            set
            {
                _taxRate = value;
                OnPropertyChanged(nameof(TaxRate));
                UpdateTotals();
            }
        }

        public decimal DiscountAmount
        {
            get => _discountAmount;
            set
            {
                _discountAmount = Math.Max(0, value);
                OnPropertyChanged(nameof(DiscountAmount));
                UpdateTotals();
            }
        }

        // Computed properties
        public decimal Subtotal => _items.Sum(item => item.Subtotal);
        public decimal TaxAmount => (Subtotal - DiscountAmount) * TaxRate;
        public decimal Total => Subtotal - DiscountAmount + TaxAmount;
        public int ItemCount => _items.Sum(item => item.Quantity);

        public string FormattedSubtotal => $"${Subtotal:F2}";
        public string FormattedTaxAmount => $"${TaxAmount:F2}";
        public string FormattedDiscountAmount => $"${DiscountAmount:F2}";
        public string FormattedTotal => $"${Total:F2}";

        public event PropertyChangedEventHandler? PropertyChanged;

        public CartService()
        {
            _items.CollectionChanged += (s, e) => UpdateTotals();
        }

        public void AddItem(Product product, int quantity = 1)
        {
            var existingItem = _items.FirstOrDefault(item => item.Product.Id == product.Id);
            if (existingItem != null)
            {
                existingItem.Quantity += quantity;
            }
            else
            {
                var newItem = new CartItem(product, quantity);
                newItem.PropertyChanged += (s, e) => UpdateTotals();
                _items.Add(newItem);
            }
        }

        public void RemoveItem(CartItem item)
        {
            _items.Remove(item);
        }

        public void UpdateQuantity(CartItem item, int newQuantity)
        {
            if (newQuantity <= 0)
            {
                RemoveItem(item);
            }
            else
            {
                item.Quantity = newQuantity;
            }
        }

        public void Clear()
        {
            _items.Clear();
        }

        public Transaction CreateTransaction(Customer? customer = null, PaymentMethod paymentMethod = PaymentMethod.Cash)
        {
            var transaction = new Transaction
            {
                Items = _items.ToList(),
                Customer = customer,
                PaymentMethod = paymentMethod,
                TaxRate = TaxRate,
                DiscountAmount = DiscountAmount,
                Status = TransactionStatus.Pending
            };

            return transaction;
        }

        private void UpdateTotals()
        {
            OnPropertyChanged(nameof(Subtotal));
            OnPropertyChanged(nameof(TaxAmount));
            OnPropertyChanged(nameof(Total));
            OnPropertyChanged(nameof(ItemCount));
            OnPropertyChanged(nameof(FormattedSubtotal));
            OnPropertyChanged(nameof(FormattedTaxAmount));
            OnPropertyChanged(nameof(FormattedTotal));
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
