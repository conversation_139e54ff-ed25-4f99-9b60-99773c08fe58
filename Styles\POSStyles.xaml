<ResourceDictionary
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Touch-friendly button styles -->
    <Style x:Key="TouchButtonStyle" TargetType="Button" BasedOn="{StaticResource DefaultButtonStyle}">
        <Setter Property="MinHeight" Value="48"/>
        <Setter Property="MinWidth" Value="120"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="CornerRadius" Value="8"/>
    </Style>

    <Style x:Key="TouchAccentButtonStyle" TargetType="Button" BasedOn="{StaticResource AccentButtonStyle}">
        <Setter Property="MinHeight" Value="48"/>
        <Setter Property="MinWidth" Value="120"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="CornerRadius" Value="8"/>
    </Style>

    <Style x:Key="LargeTouchButtonStyle" TargetType="Button" BasedOn="{StaticResource TouchButtonStyle}">
        <Setter Property="MinHeight" Value="64"/>
        <Setter Property="MinWidth" Value="160"/>
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <!-- Card container style -->
    <Style x:Key="CardContainerStyle" TargetType="Border">
        <Setter Property="Background" Value="{ThemeResource CardBackgroundFillColorDefaultBrush}"/>
        <Setter Property="CornerRadius" Value="12"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="BorderBrush" Value="{ThemeResource CardStrokeColorDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- Product card style -->
    <Style x:Key="ProductCardStyle" TargetType="Border" BasedOn="{StaticResource CardContainerStyle}">
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="MinHeight" Value="280"/>
        <Setter Property="MaxWidth" Value="200"/>
    </Style>

    <!-- Touch-friendly input controls -->
    <Style x:Key="TouchTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource DefaultTextBoxStyle}">
        <Setter Property="MinHeight" Value="44"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="CornerRadius" Value="6"/>
    </Style>

    <Style x:Key="TouchComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource DefaultComboBoxStyle}">
        <Setter Property="MinHeight" Value="44"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="CornerRadius" Value="6"/>
    </Style>

    <!-- Navigation styles -->
    <Style x:Key="POSNavigationViewStyle" TargetType="NavigationView">
        <Setter Property="PaneDisplayMode" Value="Left"/>
        <Setter Property="OpenPaneLength" Value="240"/>
        <Setter Property="CompactModeThresholdWidth" Value="0"/>
        <Setter Property="ExpandedModeThresholdWidth" Value="0"/>
        <Setter Property="IsBackButtonVisible" Value="Collapsed"/>
        <Setter Property="IsSettingsVisible" Value="False"/>
    </Style>

    <!-- Typography styles -->
    <Style x:Key="POSTitleStyle" TargetType="TextBlock" BasedOn="{StaticResource TitleTextBlockStyle}">
        <Setter Property="FontSize" Value="32"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Margin" Value="0,0,0,24"/>
    </Style>

    <Style x:Key="POSSubtitleStyle" TargetType="TextBlock" BasedOn="{StaticResource SubtitleTextBlockStyle}">
        <Setter Property="FontSize" Value="20"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
    </Style>

    <Style x:Key="POSBodyStyle" TargetType="TextBlock" BasedOn="{StaticResource BodyTextBlockStyle}">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="LineHeight" Value="24"/>
    </Style>

    <Style x:Key="POSCaptionStyle" TargetType="TextBlock" BasedOn="{StaticResource CaptionTextBlockStyle}">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="{ThemeResource TextFillColorSecondaryBrush}"/>
    </Style>

    <!-- Price display styles -->
    <Style x:Key="PriceTextStyle" TargetType="TextBlock">
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="Foreground" Value="{ThemeResource SystemAccentColor}"/>
    </Style>

    <Style x:Key="LargePriceTextStyle" TargetType="TextBlock" BasedOn="{StaticResource PriceTextStyle}">
        <Setter Property="FontSize" Value="24"/>
    </Style>

    <Style x:Key="TotalPriceTextStyle" TargetType="TextBlock" BasedOn="{StaticResource PriceTextStyle}">
        <Setter Property="FontSize" Value="28"/>
        <Setter Property="FontWeight" Value="Black"/>
    </Style>

    <!-- Status indicator styles -->
    <Style x:Key="StatusIndicatorStyle" TargetType="Ellipse">
        <Setter Property="Width" Value="12"/>
        <Setter Property="Height" Value="12"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- List item styles -->
    <Style x:Key="CartItemContainerStyle" TargetType="Border" BasedOn="{StaticResource CardContainerStyle}">
        <Setter Property="Margin" Value="0,4"/>
        <Setter Property="Padding" Value="16"/>
    </Style>

    <!-- Quantity control styles -->
    <Style x:Key="QuantityButtonStyle" TargetType="Button">
        <Setter Property="Width" Value="44"/>
        <Setter Property="Height" Value="44"/>
        <Setter Property="FontSize" Value="18"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="CornerRadius" Value="22"/>
        <Setter Property="Background" Value="{ThemeResource SystemControlBackgroundBaseLowBrush}"/>
        <Setter Property="BorderBrush" Value="{ThemeResource SystemControlForegroundBaseMediumLowBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
    </Style>

    <!-- Receipt styles -->
    <Style x:Key="ReceiptContainerStyle" TargetType="Border">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="{ThemeResource SystemControlForegroundBaseLowBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="40"/>
        <Setter Property="MaxWidth" Value="400"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
    </Style>

    <Style x:Key="ReceiptTextStyle" TargetType="TextBlock">
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="FontFamily" Value="Consolas, Courier New, monospace"/>
    </Style>

    <!-- Info bar styles for notifications -->
    <Style x:Key="POSInfoBarStyle" TargetType="InfoBar">
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="CornerRadius" Value="8"/>
    </Style>

    <!-- Grid view styles -->
    <Style x:Key="ProductGridViewStyle" TargetType="GridView">
        <Setter Property="SelectionMode" Value="None"/>
        <Setter Property="IsItemClickEnabled" Value="True"/>
        <Setter Property="ScrollViewer.HorizontalScrollMode" Value="Disabled"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled"/>
        <Setter Property="ScrollViewer.VerticalScrollMode" Value="Auto"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
    </Style>

    <!-- Payment method icon styles -->
    <Style x:Key="PaymentIconStyle" TargetType="FontIcon">
        <Setter Property="FontSize" Value="32"/>
        <Setter Property="Foreground" Value="{ThemeResource SystemAccentColor}"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Loading indicator styles -->
    <Style x:Key="ProcessingIndicatorStyle" TargetType="ProgressRing">
        <Setter Property="Width" Value="40"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="HorizontalAlignment" Value="Center"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- Spacing resources -->
    <x:Double x:Key="POSSmallSpacing">8</x:Double>
    <x:Double x:Key="POSMediumSpacing">16</x:Double>
    <x:Double x:Key="POSLargeSpacing">24</x:Double>
    <x:Double x:Key="POSExtraLargeSpacing">32</x:Double>

    <!-- Touch target sizes -->
    <x:Double x:Key="TouchTargetMinSize">44</x:Double>
    <x:Double x:Key="TouchTargetComfortableSize">48</x:Double>
    <x:Double x:Key="TouchTargetLargeSize">64</x:Double>

</ResourceDictionary>
