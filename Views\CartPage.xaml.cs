using System;
using System.Linq;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Microsoft.UI.Xaml.Navigation;
using firstapp.Models;
using firstapp.Services;

namespace firstapp.Views
{
    public sealed partial class CartPage : Page
    {
        private CartService? _cartService;

        public CartService? CartService => _cartService;

        public Visibility IsCartEmpty => _cartService?.Items.Count == 0 ? Visibility.Visible : Visibility.Collapsed;
        public Visibility IsCartNotEmpty => _cartService?.Items.Count > 0 ? Visibility.Visible : Visibility.Collapsed;

        public CartPage()
        {
            InitializeComponent();
        }

        protected override void OnNavigatedTo(NavigationEventArgs e)
        {
            base.OnNavigatedTo(e);

            if (e.Parameter is object param)
            {
                var properties = param.GetType().GetProperties();
                var cartServiceProp = properties.FirstOrDefault(p => p.PropertyType == typeof(CartService));

                if (cartServiceProp != null)
                {
                    _cartService = (CartService?)cartServiceProp.GetValue(param);
                    
                    if (_cartService != null)
                    {
                        _cartService.PropertyChanged += (s, e) => UpdateVisibility();
                        _cartService.Items.CollectionChanged += (s, e) => UpdateVisibility();
                    }
                }
            }

            UpdateVisibility();
        }

        private void UpdateVisibility()
        {
            Bindings.Update();
        }

        private void IncreaseQuantityButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is CartItem item && _cartService != null)
            {
                _cartService.UpdateQuantity(item, item.Quantity + 1);
            }
        }

        private void DecreaseQuantityButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is CartItem item && _cartService != null)
            {
                _cartService.UpdateQuantity(item, item.Quantity - 1);
            }
        }

        private void RemoveItemButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is CartItem item && _cartService != null)
            {
                _cartService.RemoveItem(item);
            }
        }

        private void ClearCartButton_Click(object sender, RoutedEventArgs e)
        {
            if (_cartService != null)
            {
                ShowClearCartConfirmation();
            }
        }

        private async void ShowClearCartConfirmation()
        {
            var dialog = new ContentDialog
            {
                Title = "Clear Cart",
                Content = "Are you sure you want to remove all items from your cart?",
                PrimaryButtonText = "Clear",
                SecondaryButtonText = "Cancel",
                DefaultButton = ContentDialogButton.Secondary,
                XamlRoot = this.XamlRoot
            };

            var result = await dialog.ShowAsync();
            if (result == ContentDialogResult.Primary && _cartService != null)
            {
                _cartService.Clear();
            }
        }

        private void CheckoutButton_Click(object sender, RoutedEventArgs e)
        {
            if (_cartService?.Items.Count > 0)
            {
                // Navigate to checkout page
                if (Frame.Parent is NavigationView navView)
                {
                    // Find and select the Checkout navigation item
                    var checkoutItem = navView.MenuItems.OfType<NavigationViewItem>()
                        .FirstOrDefault(item => item.Tag?.ToString() == "Checkout");
                    
                    if (checkoutItem != null)
                    {
                        navView.SelectedItem = checkoutItem;
                    }
                }
            }
        }
    }
}
