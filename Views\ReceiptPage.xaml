<?xml version="1.0" encoding="utf-8"?>
<Page
    x:Class="firstapp.Views.ReceiptPage"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:local="using:firstapp.Views"
    xmlns:models="using:firstapp.Models"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    mc:Ignorable="d">

    <Grid Padding="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Grid Grid.Row="0" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>
            
            <TextBlock Grid.Column="0"
                       Text="Transaction Complete" 
                       Style="{StaticResource TitleTextBlockStyle}"/>
            
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Content="Print Receipt"
                        Click="PrintReceiptButton_Click"
                        Style="{StaticResource DefaultButtonStyle}"
                        Margin="0,0,10,0">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <FontIcon Glyph="&#xE749;" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="Print"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
                
                <Button Content="Email Receipt"
                        Click="EmailReceiptButton_Click"
                        Style="{StaticResource DefaultButtonStyle}">
                    <Button.Content>
                        <StackPanel Orientation="Horizontal">
                            <FontIcon Glyph="&#xE715;" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="Email"/>
                        </StackPanel>
                    </Button.Content>
                </Button>
            </StackPanel>
        </Grid>

        <!-- Receipt Display -->
        <ScrollViewer Grid.Row="1" ZoomMode="Enabled" HorizontalScrollMode="Auto" HorizontalScrollBarVisibility="Auto">
            <Border x:Name="ReceiptBorder"
                    Background="White"
                    BorderBrush="{ThemeResource SystemControlForegroundBaseLowBrush}"
                    BorderThickness="1"
                    CornerRadius="4"
                    Padding="40"
                    MaxWidth="400"
                    HorizontalAlignment="Center">
                <Border.Shadow>
                    <ThemeShadow />
                </Border.Shadow>

                <StackPanel x:Name="ReceiptContent">
                    <!-- Store Header -->
                    <TextBlock Text="RETAIL POS SYSTEM" 
                               FontSize="20" 
                               FontWeight="Bold" 
                               HorizontalAlignment="Center"
                               Foreground="Black"
                               Margin="0,0,0,4"/>
                    
                    <TextBlock Text="123 Main Street" 
                               FontSize="12" 
                               HorizontalAlignment="Center"
                               Foreground="Black"
                               Margin="0,0,0,2"/>
                    
                    <TextBlock Text="City, State 12345" 
                               FontSize="12" 
                               HorizontalAlignment="Center"
                               Foreground="Black"
                               Margin="0,0,0,2"/>
                    
                    <TextBlock Text="Phone: (*************" 
                               FontSize="12" 
                               HorizontalAlignment="Center"
                               Foreground="Black"
                               Margin="0,0,0,16"/>

                    <!-- Transaction Info -->
                    <Border Height="1" Background="Black" Margin="0,0,0,8"/>
                    
                    <Grid Margin="0,0,0,4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Transaction #:" FontSize="12" Foreground="Black"/>
                        <TextBlock Grid.Column="1" x:Name="TransactionNumberText" FontSize="12" Foreground="Black"/>
                    </Grid>
                    
                    <Grid Margin="0,0,0,4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Date:" FontSize="12" Foreground="Black"/>
                        <TextBlock Grid.Column="1" x:Name="TransactionDateText" FontSize="12" Foreground="Black"/>
                    </Grid>
                    
                    <Grid Margin="0,0,0,4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Time:" FontSize="12" Foreground="Black"/>
                        <TextBlock Grid.Column="1" x:Name="TransactionTimeText" FontSize="12" Foreground="Black"/>
                    </Grid>
                    
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Payment Method:" FontSize="12" Foreground="Black"/>
                        <TextBlock Grid.Column="1" x:Name="PaymentMethodText" FontSize="12" Foreground="Black"/>
                    </Grid>

                    <!-- Items Header -->
                    <Border Height="1" Background="Black" Margin="0,0,0,8"/>
                    
                    <Grid Margin="0,0,0,8">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="ITEM" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                        <TextBlock Grid.Column="1" Text="QTY" FontSize="12" FontWeight="Bold" Foreground="Black" Margin="8,0"/>
                        <TextBlock Grid.Column="2" Text="TOTAL" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                    </Grid>

                    <!-- Items List -->
                    <ItemsControl x:Name="ReceiptItemsControl" Margin="0,0,0,16">
                        <ItemsControl.ItemTemplate>
                            <DataTemplate x:DataType="models:CartItem">
                                <StackPanel Margin="0,0,0,8">
                                    <!-- Item Name -->
                                    <TextBlock Text="{x:Bind Product.Name}" 
                                               FontSize="12" 
                                               FontWeight="SemiBold"
                                               Foreground="Black"
                                               TextWrapping="Wrap"/>
                                    
                                    <!-- Item Details -->
                                    <Grid Margin="0,2,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <TextBlock Grid.Column="0" 
                                                   Text="{x:Bind Product.FormattedPrice}" 
                                                   FontSize="11" 
                                                   Foreground="Black"/>
                                        <TextBlock Grid.Column="1" 
                                                   Text="{x:Bind Quantity}" 
                                                   FontSize="11" 
                                                   Foreground="Black"
                                                   Margin="8,0"/>
                                        <TextBlock Grid.Column="2" 
                                                   Text="{x:Bind FormattedSubtotal}" 
                                                   FontSize="11" 
                                                   Foreground="Black"/>
                                    </Grid>
                                </StackPanel>
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>

                    <!-- Totals Section -->
                    <Border Height="1" Background="Black" Margin="0,0,0,8"/>
                    
                    <Grid Margin="0,0,0,4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Subtotal:" FontSize="12" Foreground="Black"/>
                        <TextBlock Grid.Column="1" x:Name="SubtotalText" FontSize="12" Foreground="Black"/>
                    </Grid>

                    <Grid x:Name="DiscountRow" Margin="0,0,0,4" Visibility="Collapsed">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Discount:" FontSize="12" Foreground="Black"/>
                        <TextBlock Grid.Column="1" x:Name="DiscountText" FontSize="12" Foreground="Black"/>
                    </Grid>
                    
                    <Grid Margin="0,0,0,4">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="Tax:" FontSize="12" Foreground="Black"/>
                        <TextBlock Grid.Column="1" x:Name="TaxText" FontSize="12" Foreground="Black"/>
                    </Grid>
                    
                    <Border Height="2" Background="Black" Margin="0,8,0,8"/>
                    
                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <TextBlock Grid.Column="0" Text="TOTAL:" FontSize="16" FontWeight="Bold" Foreground="Black"/>
                        <TextBlock Grid.Column="1" x:Name="TotalText" FontSize="16" FontWeight="Bold" Foreground="Black"/>
                    </Grid>

                    <!-- Payment Details -->
                    <StackPanel x:Name="CashPaymentDetails" Visibility="Collapsed" Margin="0,0,0,16">
                        <Grid Margin="0,0,0,4">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Cash Received:" FontSize="12" Foreground="Black"/>
                            <TextBlock Grid.Column="1" x:Name="CashReceivedText" FontSize="12" Foreground="Black"/>
                        </Grid>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" Text="Change:" FontSize="12" Foreground="Black"/>
                            <TextBlock Grid.Column="1" x:Name="ChangeText" FontSize="12" Foreground="Black"/>
                        </Grid>
                    </StackPanel>

                    <!-- Footer -->
                    <Border Height="1" Background="Black" Margin="0,0,0,8"/>
                    
                    <TextBlock Text="Thank you for your business!" 
                               FontSize="12" 
                               HorizontalAlignment="Center"
                               Foreground="Black"
                               Margin="0,0,0,8"/>
                    
                    <TextBlock Text="Please keep this receipt for your records" 
                               FontSize="10" 
                               HorizontalAlignment="Center"
                               Foreground="Black"/>
                </StackPanel>
            </Border>
        </ScrollViewer>

        <!-- Action Buttons -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center"
                    Margin="0,20,0,0">
            <Button Content="New Transaction"
                    Click="NewTransactionButton_Click"
                    Style="{StaticResource AccentButtonStyle}"
                    Margin="0,0,10,0"/>
            <Button Content="Back to Products"
                    Click="BackToProductsButton_Click"
                    Style="{StaticResource DefaultButtonStyle}"/>
        </StackPanel>
    </Grid>
</Page>
